/**
 * @Author: Rain
 * @Date: 2025/07/15 09:16:11
 * @LastEditTime: 2025/07/20 22:26:15
 * @Description: Do Something
 */
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup
} from 'unocss'

// @docs https://github.com/unocss-applet/unocss-applet/tree/main
/** 在小程序(UniApp 和 Taro)中使用UnoCSS，兼容不支持的语法 */
import {
  presetApplet,
  presetRemRpx,
  transformerAttributify
} from 'unocss-applet'

// @docs https://unocss.dev/presets/legacy-compat
import presetLegacyCompat from '@unocss/preset-legacy-compat'
const isApplet = process.env?.UNI_PLATFORM?.startsWith('mp-') ?? false
const transformers = []
const presets = []
if (isApplet) {
  // 解决第三方样式冲突问题
  transformers.push(
    transformerAttributify({ prefixedOnly: true, prefix: 'sn' })
  )
} else {
  presets.push(presetRemRpx({ mode: 'rpx2rem' }))
}

presets.push(presetApplet())
presets.push(presetAttributify())

export default defineConfig({
  presets: [
    ...presets,
    // 配置图标
    presetIcons({
      scale: 1.2,
      warn: true,
      autoInstall: true,
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle'
      }
    }),
    // 兼容app端颜色值
    presetLegacyCompat({
      commaStyleColorFunction: true
    })
  ],
  /**
   * 自定义快捷语句
   * @docs https://unocss.dev/config/shortcuts
   */
  shortcuts: [
    {
      'sn-btn-default': 'text-gray-400 border-rd-20 after:border-none'
    }
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
    ...transformers
  ],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)'
      }
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
    [/^m-(\d+)$/, ([, d]) => ({ margin: `${d}rpx` })],
    [/^mt-(\d+)$/, ([, d]) => ({ 'margin-top': `${d}rpx` })],
    [/^mb-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d}rpx` })],
    [/^ml-(\d+)$/, ([, d]) => ({ 'margin-left': `${d}rpx` })],
    [/^mr-(\d+)$/, ([, d]) => ({ 'margin-right': `${d}rpx` })],
    [
      /^mx-(\d+)$/,
      ([, d]) => ({
        'margin-left': `${d}rpx`,
        'margin-right': `${d}rpx`
      })
    ],
    [
      /^my-(\d+)$/,
      ([, d]) => ({
        'margin-top': `${d}rpx`,
        'margin-bottom': `${d}rpx`
      })
    ],
    [/^p-(\d+)$/, ([, d]) => ({ padding: `${d}rpx` })],
    [/^pt-(\d+)$/, ([, d]) => ({ 'padding-top': `${d}rpx` })],
    [/^pb-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d}rpx` })],
    [/^pl-(\d+)$/, ([, d]) => ({ 'padding-left': `${d}rpx` })],
    [/^pr-(\d+)$/, ([, d]) => ({ 'padding-right': `${d}rpx` })],
    [
      /^px-(\d+)$/,
      ([, d]) => ({
        'padding-left': `${d}rpx`,
        'padding-right': `${d}rpx`
      })
    ],
    [
      /^py-(\d+)$/,
      ([, d]) => ({
        'padding-top': `${d}rpx`,
        'padding-bottom': `${d}rpx`
      })
    ]
  ]
})
