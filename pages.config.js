/**
 * @Author: Rain
 * @Date: 2025/07/15 09:16:11
 * @LastEditTime: 2025/07/26 15:57:07
 * @Description: Do Something
 */
import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'
import { name } from './package.json'

/**
 * 全局配置
 */
export default defineUniPages({
  pages: [],
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: name,
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF'
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue'
    }
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#0B9BFF',
    backgroundColor: '#ffffff',
    borderStyle: 'white',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: '/static/tabbar/home_default.png',
        selectedIconPath: '/static/tabbar/home_active.png',
        pagePath: 'pages/index/index',
        text: '首页'
      },
      {
        iconPath: '/static/tabbar/my_default.png',
        selectedIconPath: '/static/tabbar/my_active.png',
        pagePath: 'pages/my/index',
        text: '我的'
      }
    ]
  }
})
