/*
 * @Author: Rain
 * @Date: 2025/07/18 15:56:25
 * @LastEditTime: 2025/08/11 23:01:54
 * @Description: Do Something
 */
import { defineConfig, globalIgnores } from 'eslint/config'
import globals from 'globals'
import js from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import pluginOxlint from 'eslint-plugin-oxlint'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
const { default: autoImportRules } = await import(
  './.eslintrc-auto-import.json',
  {
    with: { type: 'json' }
  }
)
export default defineConfig([
  {
    name: 'app/files-to-lint',
    files: ['**/*.{js,mjs,jsx,vue}']
  },
  globalIgnores([
    '**/node_modules',
    '**/uni_modules',
    '**/dist',
    '**/pnpm-lock.yaml',
    '**/yarn.lock',
    '**/*.log',
    '**/template',
    'dist/**',
    '.history/**',
    '**/types/**',
    '**/code/**',
    '**/mescroll-uni/**'
  ]),
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...autoImportRules.globals,
        $t: true,
        uni: true,
        UniApp: true,
        wx: true,
        WechatMiniprogram: true,
        getCurrentPages: true,
        UniHelper: true,
        Page: true,
        App: true,
        NodeJS: true
      }
    }
  },
  js.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  ...pluginOxlint.configs['flat/recommended'],
  {
    rules: {
      // 基础语法规则
      'no-console': 'off', // 允许console但警告
      'no-unused-vars': ['error', { vars: 'all', args: 'after-used' }], // 禁止未使用变量
      eqeqeq: ['error', 'always'], // 强制使用===和!==
      semi: ['error', 'always'], // 强制使用分号
      quotes: ['error', 'single'], // 强制单引号
      indent: ['error', 2, { SwitchCase: 1 }], // 2空格缩进，switch case缩进1格
      'no-trailing-spaces': 'error', // 禁止行尾空格
      'eol-last': ['error', 'always'], // 文件末尾强制空行
      // Vue特定规则
      'vue/multi-word-component-names': 'off', // 关闭组件名多单词检查
      'vue/no-v-model-argument': 'error', // 禁止v-model带参数
      'vue/no-unused-vars': 'error'
    }
  },
  skipFormatting
])
