{"root": true, "extends": ["stylelint-config-recommended", "stylelint-config-recommended-scss", "stylelint-config-recommended-vue/scss", "stylelint-config-html/vue", "stylelint-config-recess-order"], "plugins": ["stylelint-prettier"], "overrides": [{"files": ["**/*.{vue,html}"], "customSyntax": "postcss-html"}, {"files": ["**/*.{css,scss}"], "customSyntax": "postcss-scss"}], "rules": {"prettier/prettier": true, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "export", "v-deep", "deep"]}], "unit-no-unknown": [true, {"ignoreUnits": ["rpx"]}], "selector-type-no-unknown": [true, {"ignoreTypes": ["page"]}], "comment-empty-line-before": "never", "custom-property-empty-line-before": "never", "no-empty-source": null, "comment-no-empty": null, "no-duplicate-selectors": null, "scss/comment-no-empty": null, "selector-class-pattern": null, "font-family-no-missing-generic-family-keyword": null}}