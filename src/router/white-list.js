/**
 * @Author: Rain
 * @Date: 2025/07/17 20:54:50
 * @LastEditTime: 2025/08/07 22:27:14
 * @Description: Do Something
 */
const { VITE_OPNE_NO_LOGIN } = import.meta.env
/**
 * !!! 注： whiteListByPath 和 loginList不能同时生效
 * VITE_OPNE_NO_LOGIN 为true 设置whiteListByPath，反之设置loginList
 * 请根据自己的业务进行调整
 */
/** 免登录白名单（匹配路由 path） */
const whiteListByPath = ['/sub-pages/login/index', '/sub-pages/login/sms']

/** 需要登录的白名单 匹配路由 path） 如：['/sub-pages/detail/index'] */
const loginList = []

/** 判断是否在白名单 */
export const isWhiteList = (to) => {
  if (JSON.parse(VITE_OPNE_NO_LOGIN)) return whiteListByPath.indexOf(to) !== -1

  return loginList.indexOf(to) === -1
}
