/**
 * @Author: Rain
 * @Date: 2025/07/15 09:16:11
 * @LastEditTime: 2025/08/07 22:02:36
 * @Description: Do Something
 */
import { useUserStore } from '@/store'
import { isWhiteList } from '@/router'

const navigateToInterceptor = {
  invoke({ url }) {
    // 判断是否登录
    if (useUserStore().token) {
      return true
    }
    const flag = isWhiteList(url)
    if (!flag) return uni.navigateTo({ url: '/sub-pages/login/index' })
    return flag
  }
}

export const routeInterceptor = {
  install() {
    uni.addInterceptor('navigateTo', navigateToInterceptor)
    uni.addInterceptor('reLaunch', navigateToInterceptor)
    uni.addInterceptor('redirectTo', navigateToInterceptor)
    uni.addInterceptor('switchTab', navigateToInterceptor)
    uni.addInterceptor('Launch', navigateToInterceptor)
  }
}
