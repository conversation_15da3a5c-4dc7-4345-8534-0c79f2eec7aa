/**
 * @Author: Rain
 * @Date: 2025/07/20 16:50:00
 * @Description: 错误处理工具函数
 */

// 错误类型枚举
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 错误码映射
export const ErrorCodeMap = {
  400: { type: ErrorTypes.VALIDATION_ERROR, message: '请求参数错误' },
  401: { type: ErrorTypes.AUTH_ERROR, message: '请先登录' },
  403: { type: ErrorTypes.PERMISSION_ERROR, message: '权限不足' },
  404: { type: ErrorTypes.NOT_FOUND_ERROR, message: '资源不存在' },
  500: { type: ErrorTypes.API_ERROR, message: '服务器内部错误' },
  502: { type: ErrorTypes.NETWORK_ERROR, message: '网络连接异常' },
  503: { type: ErrorTypes.API_ERROR, message: '服务暂时不可用' },
  504: { type: ErrorTypes.NETWORK_ERROR, message: '网络超时' }
}

/**
 * 统一错误处理函数
 * @param {Error|Object} error - 错误对象
 * @param {string} context - 错误上下文
 * @param {Object} options - 处理选项
 */
export const handleError = (error, context = '', options = {}) => {
  const {
    showToast = true,
    showModal = false,
    autoRedirect = true,
    defaultMessage = '操作失败'
  } = options

  console.error(`[${context}] Error:`, error)

  let errorInfo = {
    type: ErrorTypes.UNKNOWN_ERROR,
    message: defaultMessage,
    code: 0
  }

  // 解析错误信息
  if (error?.response) {
    // HTTP错误
    const { status, data } = error.response
    const mappedError = ErrorCodeMap[status]

    if (mappedError) {
      errorInfo = {
        type: mappedError.type,
        message: data?.message || mappedError.message,
        code: status
      }
    }
  } else if (error?.code) {
    // 业务错误
    errorInfo = {
      type: ErrorTypes.API_ERROR,
      message: error.message || defaultMessage,
      code: error.code
    }
  } else if (error?.message) {
    // 其他错误
    errorInfo = {
      type: ErrorTypes.UNKNOWN_ERROR,
      message: error.message,
      code: 0
    }
  }

  // 显示错误提示
  if (showToast) {
    uni.showToast({
      title: errorInfo.message,
      icon: 'error',
      duration: 2000
    })
  }

  if (showModal) {
    uni.showModal({
      title: '错误提示',
      content: errorInfo.message,
      showCancel: false
    })
  }

  // 自动跳转处理
  if (autoRedirect) {
    handleAutoRedirect(errorInfo)
  }

  return errorInfo
}

/**
 * 自动跳转处理
 * @param {Object} errorInfo - 错误信息
 */
const handleAutoRedirect = (errorInfo) => {
  switch (errorInfo.type) {
    case ErrorTypes.AUTH_ERROR:
      // 未登录，跳转到登录页
      setTimeout(() => {
        uni.navigateTo({
          url: '/sub-pages/login/index'
        })
      }, 1500)
      break

    case ErrorTypes.NOT_FOUND_ERROR:
      // 资源不存在，返回上一页
      setTimeout(() => {
        uni.navigateBack({
          delta: 1
        })
      }, 2000)
      break

    case ErrorTypes.PERMISSION_ERROR:
      // 权限不足，返回首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }, 2000)
      break
  }
}

/**
 * 网络错误处理
 * @param {Object} error - 网络错误
 */
export const handleNetworkError = (error) => {
  return handleError(error, 'Network', {
    defaultMessage: '网络连接失败，请检查网络设置'
  })
}

/**
 * API错误处理
 * @param {Object} error - API错误
 * @param {string} apiName - API名称
 */
export const handleApiError = (error, apiName = '') => {
  return handleError(error, `API-${apiName}`, {
    defaultMessage: '服务异常，请稍后重试'
  })
}

/**
 * 表单验证错误处理
 * @param {Object} errors - 验证错误
 */
export const handleValidationError = (errors) => {
  const firstError = Object.values(errors)[0]
  return handleError({ message: firstError }, 'Validation', {
    defaultMessage: '请检查输入内容'
  })
}

/**
 * 创建错误边界
 * @param {Function} fn - 要执行的函数
 * @param {string} context - 上下文
 * @param {Object} options - 选项
 */
export const createErrorBoundary = (fn, context, options = {}) => {
  return async (...args) => {
    try {
      return await fn(...args)
    } catch (error) {
      handleError(error, context, options)
      throw error
    }
  }
}

/**
 * 重试机制
 * @param {Function} fn - 要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟(ms)
 */
export const withRetry = async (fn, maxRetries = 3, delay = 1000) => {
  let lastError

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      if (i === maxRetries) {
        throw error
      }

      // 延迟重试
      await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)))
    }
  }

  throw lastError
}

/**
 * 防抖错误处理
 */
const errorDebounceMap = new Map()

export const debounceError = (key, error, context, options = {}) => {
  if (errorDebounceMap.has(key)) {
    clearTimeout(errorDebounceMap.get(key))
  }

  const timer = setTimeout(() => {
    handleError(error, context, options)
    errorDebounceMap.delete(key)
  }, 500)

  errorDebounceMap.set(key, timer)
}
