/**
 * @Author: Rain
 * @Date: 2025/08/09 21:29:14
 * @LastEditTime: 2025/08/11 13:35:39
 * @Description: 登录相关
 */
import { request } from '@/utils/request'

/**
 * 微信登录
 * @param {*} params
 * @returns
 */
export const wxLogin = (params) =>
  request({
    url: `/user_center/api/wxlogin.action`,
    method: 'POST',
    data: params
  })

/**
 * 手机号和验证码登录
 * @param {*} params
 * @returns
 */
export const phoneLogin = (params) =>
  request({
    url: `/user_center/storeweb/login.action`,
    method: 'POST',
    data: params
  })
