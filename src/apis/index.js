/**
 * @Author: Rain
 * @Date: 2025/08/11 13:35:18
 * @LastEditTime: 2025/08/11 16:09:06
 * @Description: 首页接口
 */

import { request } from '@/utils/request'

/**
 * @description: 查询今日任务
 * @param {*} params
 * @return {*}
 */
export const GetTodayTask = (data) =>
  request({
    url: `/store-web-new/attendance/queryAttendanceListByToday.action`,
    method: 'POST',
    data
  })

/**
 * @description: 我加入的
 * @param {*} params
 * @return {*}
 */
export const GetMyJoin = (data) =>
  request({
    url: `/store-web-new/attendance/queryAttendanceListByMember.action`,
    method: 'POST',
    data
  })

/**
 * @description: 我创建的
 * @param {*} params
 * @return {*}
 */
export const GetMyCreate = (data) =>
  request({
    url: `/store-web-new/attendance/queryAttendanceListByCreate.action`,
    method: 'POST',
    data
  })
