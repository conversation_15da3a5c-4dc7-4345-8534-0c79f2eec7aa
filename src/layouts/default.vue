<!--
 * @Author: Rain
 * @Date: 2025/07/17 20:54:50
 * @LastEditTime: 2025/07/17 22:27:03
 * @Description: Do Something
-->
<template>
  <wd-config-provider
    theme="light"
    :theme-vars="themeVars"
    custom-class="w-full h-full overflow-hidden"
  >
    <view class="defaul-app-layout w-full h-full overflow-hidden">
      <slot />
      <wd-toast />
      <wd-message-box />
    </view>
  </wd-config-provider>
</template>

<script setup>
const themeVars = reactive({
  colorTheme: '#2d8ff0',
  inputNotEmptyBorderColor: '#dadada'
})
</script>
