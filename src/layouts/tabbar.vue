<template>
  <wd-config-provider
    theme="light"
    :theme-vars="themeVars"
    custom-class="w-full h-full overflow-hidden"
  >
    <view class="tabbar-app-layout">
      <slot />
      <wd-toast />
      <wd-message-box />
    </view>
  </wd-config-provider>
</template>

<script setup>
const themeVars = reactive({
  colorTheme: '#2d8ff0'
})
</script>

<style lang="scss" scoped>
.tabbar-app-layout {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
}
</style>
