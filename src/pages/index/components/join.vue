<!--
 * @Author: Rain
 * @Date: 2025/07/19 15:33:52
 * @LastEditTime: 2025/07/20 13:53:21
 * @Description: Do Something
-->
<template>
  <view
    class="bg-#F2F9FF rounded-3 p-20 mb-30 shadow-sm box-border flex flex-col"
    @tap="toDetailHandler"
  >
    <view class="flex items-start">
      <!-- 左侧图片区域 -->
      <image
        class="w-22 h-22 rounded-2 flex-shrink-0 bg-gray-100 mr-20"
        :src="task.image || ''"
        mode="aspectFill"
      >
      </image>

      <!-- 右侧内容区域 -->
      <view class="flex-1 w-full flex flex-col justify-around h-22">
        <!-- 标题行 -->
        <view class="flex justify-between items-center w-full">
          <text
            class="text-28rpx font-600 text-##333333 flex-1 mr-4 overflow-hidden text-ellipsis whitespace-nowrap"
          >
            {{ task.title }}
          </text>
          <wd-icon
            name="jump"
            size="36rpx"
            color="#666666"
            @tap="shareHandler"
          ></wd-icon>
        </view>

        <!-- 累计打卡与状态 -->
        <view class="flex justify-between items-center">
          <text class="text-#666 text-28rpx">
            累计打卡{{ task.totalDays }}天
          </text>
          <view
            class="bg-#fff text-red-500 text-24rpx px-10 py-4 rounded-full font-500"
          >
            今日未打卡
          </view>
        </view>

        <!-- 进度条 -->
        <view class="flex items-center">
          <wd-progress
            :percentage="task.progressValue"
            :stroke-width="6"
            color="#3b82f6"
            :show-text="false"
          />
          <!-- 进度百分比 -->
          <view class="flex justify-end mt-2">
            <text class="text-blue-500 text-sm font-medium">{{
              task.progress
            }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="flex justify-between items-center text-sm pt-1">
      <text class="text-gray-500 leading-relaxed">{{ task.dateRange }}</text>
      <view class="flex items-center space-x-5">
        <text class="text-#00ca5d font-500 text-24rpx">进行中</text>
        <text class="text-#333 text-24rpx"
          >已有{{ task.participants }}人参与</text
        >
      </view>
    </view>
  </view>
</template>

<script setup>
/**
 * 打卡任务项组件
 * @param {Object} task - 任务数据对象
 * @property {string} title - 任务标题
 * @property {number} totalDays - 累计打卡天数
 * @property {string} progress - 进度百分比字符串（如"20%"）
 * @property {number} progressValue - 进度数值（如20）
 * @property {string} dateRange - 日期范围字符串
 * @property {number} participants - 参与人数
 * @property {string} image - 图片链接
 */
defineProps({
  task: {
    type: Object,
    default: () => ({
      title: '英语口语朗读每日打卡',
      totalDays: 20,
      progress: '20%',
      progressValue: 20,
      dateRange: '2025.06.01到2026.06.30',
      participants: 10000,
      image: ''
    })
  }
})
//分享页面
const shareHandler = () => {
  //跳转 share/index
  uni.navigateTo({
    url: '/sub-pages/share/index'
  })
}

const toDetailHandler = () => {
  uni.navigateTo({
    url: '/sub-pages/task/index'
  })
}
</script>
