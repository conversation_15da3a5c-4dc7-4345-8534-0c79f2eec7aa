<!--
 * @Author: Rain
 * @Date: 2025/07/17 17:03:12
 * @LastEditTime: 2025/08/11 14:00:52
 * @Description: 登录引导
-->
<template>
  <div class="w-full h-full flex items-center justify-center flex-col">
    <text class="text-#666666 text-28rpx mb-120rpx font-500"
      >登录后可查看今日打卡任务</text
    >
    <view class="w-full flex items-center justify-center">
      <wd-button
        type="primary"
        @tap="goToLogin"
        custom-class="w-70% h-88rpx! font-500! min-h-88rpx! text-32rpx!"
        >去登录</wd-button
      >
    </view>
  </div>
</template>

<script setup>
const goToLogin = () => {
  uni.navigateTo({
    url: '/sub-pages/login/index'
  })
}
</script>
<style scoped lang="scss"></style>
