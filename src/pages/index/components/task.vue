<!--
 * @Author: Rain
 * @Date: 2025/07/19 15:19:39
 * @LastEditTime: 2025/07/21 11:24:30
 * @Description: 我的任务组件
-->
<template>
  <view class="flex-col flex items-center mb-20 box-border" @tap="toTaskDetail">
    <view
      class="text-[#666666] text-28rpx w-full flex items-center mb-10 pl-20 box-border"
      >{{ task.category }}</view
    >
    <view
      class="w-full flex flex-col pl-20 rounded-[20rpx] box-border bg-white min-h-130rpx h-130rpx justify-around overflow-hidden task-item"
    >
      <view class="text-[#333333] font-600 text-28rpx">{{ task.title }}</view>
      <view class="text-#F24B51 text-28rpx">{{ task.status }}</view>
    </view>
  </view>
</template>

<script setup>
defineProps({
  task: {
    type: Object,
    default: () => ({
      category: '',
      title: '',
      status: ''
    })
  }
})
const toTaskDetail = () => {
  uni.navigateTo({
    url: '/sub-pages/task/index'
  })
}
</script>
<style scoped lang="scss">
.task-item {
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.07);
}
</style>
