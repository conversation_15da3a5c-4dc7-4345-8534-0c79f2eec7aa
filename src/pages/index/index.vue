<!--
 * @Author: Rain
 * @Date: 2025/07/15 09:16:11
 * @LastEditTime: 2025/08/11 21:48:36
 * @Description: Do Something
-->
<!-- 使用 type="home" 属性设置首页 -->
<route type="home" lang="json">
{
  "layout": "tabbar",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  },
  "name": "home"
}
</route>

<template>
  <view
    class="bg-[#ffffff] min-h-screen overflow-hidden h-full w-full flex flex-col"
  >
    <view class="w-full h-480rpx overflow-hidden min-h-480rpx">
      <swiper
        :list="swiperList"
        autoplay
        indicator="none"
        class="w-full h-full"
      >
        <swiper-item v-for="(item, index) in swiperList" :key="index">
          <wd-img custom-class="w-full h-full" :src="item.image" />
        </swiper-item>
      </swiper>
    </view>
    <view
      class="bg-white rounded-t-32rpx -mt-60rpx relative flex-1 p-[28rpx_30rpx_0] box-border overflow-hidden flex flex-col"
    >
      <view class="w-full flex items-center">
        <view
          @tap="activeTab = 0"
          class="text-28rpx font-400 mr-40rpx transition-color duration-300 flex items-center justify-center"
          :class="[
            activeTab === 0 ? 'text-[#2D8FF0] font-600 active' : 'text-#666666'
          ]"
          >今日任务</view
        >
        <view
          @tap="activeTab = 1"
          class="text-28rpx font-400 transition-color duration-300 flex items-center justify-center mr-40rpx"
          :class="[
            activeTab === 1 ? 'text-[#2D8FF0] font-600 active' : 'text-#666666'
          ]"
          >我加入的</view
        >
        <view
          @tap="activeTab = 2"
          v-if="memberInfo.memberType === '老师'"
          class="text-28rpx font-400 transition-color duration-300 flex items-center justify-center"
          :class="[
            activeTab === 2 ? 'text-[#2D8FF0] font-600 active' : 'text-#666666'
          ]"
          >我创建的</view
        >
      </view>
      <view class="w-full flex-1 p-t-40rpx box-border overflow-hidden">
        <Auth class="w-full h-full min-h-full" v-if="!token"></Auth>
        <template v-else>
          <swiper :current="activeTab" class="w-full h-full">
            <swiper-item class="w-full h-full">
              <MescrollList #default="{ item }" :request="GetTodayTask">
                <Task :task="item" />
              </MescrollList>
            </swiper-item>
            <swiper-item class="w-full h-full">
              <MescrollList>
                <Join
                  v-for="(task, index) in taskList"
                  :key="index"
                  :task="task"
                />
              </MescrollList>
            </swiper-item>
            <swiper-item class="w-full h-full">
              <MescrollList>
                <Join
                  v-for="(task, index) in taskList"
                  :key="index"
                  :task="task"
                />
              </MescrollList>
            </swiper-item>
          </swiper>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
import { GetTodayTask } from '@/apis/index'
import { useUserStore } from '@/store'
const useStore = useUserStore()
const token = computed(() => useStore.token)
const memberInfo = computed(() => useStore.userInfo)
const activeTab = ref(0)
const swiperList = ref([
  {
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
])
const taskList = ref([])
</script>

<style lang="scss" scoped>
.active {
  position: relative;
  &::after {
    position: absolute;
    bottom: -16rpx;
    left: 50%;
    width: 12rpx;
    height: 12rpx;
    margin-left: -12rpx;
    content: '';
    background: #2d8ff0;
    border-radius: 50%;
  }
}
</style>
