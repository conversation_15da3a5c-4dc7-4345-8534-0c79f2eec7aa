<!--
 * @Author: Rain
 * @Date: 2025/07/17 20:54:50
 * @LastEditTime: 2025/08/07 22:28:50
 * @Description: Do Something
-->
<template>
  <view class="w-full h-full overflow-hidden">
    <view class="w-full navbar">
      <wd-navbar
        title="我的"
        custom-class="pt-88!"
        custom-style="background-color: transparent !important;"
      ></wd-navbar>
    </view>
    <view class="w-full flex items-center">
      <image src="" mode="scaleToFill" class="w-112rpx h-112rpx mr-16" />
      <text class="text-#333333 text-36rpx font-600 mr-10"
        >姓名最长就到这里</text
      >
      <wd-icon
        name="edit-outline"
        size="18px"
        color="#2D8FF0"
        @tap="toEdit"
      ></wd-icon>
    </view>
    <view class="w-full flex flex-col p-30 box-border">
      <view
        class="w-full flex items-center text-#666666 text-28rpx p-30 py-30 box-border b-b-solid b-b-[2rpx] b-b-#eeeeee"
      >
        <view
          class="i-icon-park-outline-calendar text-36rpx text-#666666 mr-16"
        ></view>
        <text class="flex-1">我的打卡动态</text>
        <wd-icon
          name="arrow-right"
          size="18px"
          color="#999999"
          @tap="toMyDynamic"
        ></wd-icon>
      </view>
      <view
        class="w-full flex items-center text-#666666 text-28rpx p-30 py-20 box-border"
      >
        <view
          class="i-material-symbols-light-settings-outline-rounded text-42rpx text-#666666 mr-16"
        ></view>
        <text class="flex-1">打卡提醒设置</text>
        <wd-icon
          name="arrow-right"
          size="18px"
          color="#999999"
          @tap="toMySetting"
        ></wd-icon>
      </view>
    </view>
  </view>
</template>

<script setup>
//我的打卡动态
const toMyDynamic = () => {
  uni.navigateTo({
    url: '/sub-pages/clock-dynamic/index'
  })
}
//打卡提醒设置
const toMySetting = () => {
  uni.navigateTo({
    url: '/sub-pages/clock-setting/index'
  })
}
//编辑信息
const toEdit = () => {
  uni.navigateTo({
    url: '/sub-pages/edit-info/index'
  })
}
</script>

<style scoped lang="scss">
.navbar {
  background: linear-gradient(to right, #dcedff, #b3cdfd);
}
</style>

<route lang="json">
{
  "layout": "tabbar",
  "style": { "navigationBarTitleText": "我的", "navigationStyle": "custom" },
  "name": "my"
}
</route>
