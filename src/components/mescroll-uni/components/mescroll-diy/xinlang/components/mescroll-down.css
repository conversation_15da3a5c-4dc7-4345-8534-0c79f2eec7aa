/*下拉刷新--上下箭头*/
.mescroll-downwarp .downwarp-arrow {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 10px;
  vertical-align: middle;
  background-image: url(https://www.mescroll.com/img/xinlang/mescroll-arrow.png);
  background-size: contain;
  transition: all 300ms;
}
/*下拉刷新--旋转进度条*/
.mescroll-downwarp .downwarp-progress {
  width: 36px;
  height: 36px;
  margin: auto;
  background-size: contain;
  border: none;
  animation: progressRotate 0.6s steps(6, start) infinite;
}
@keyframes progressRotate {
  0% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress1.png);
  }
  16% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress2.png);
  }
  32% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress3.png);
  }
  48% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress4.png);
  }
  64% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress5.png);
  }
  80% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress6.png);
  }
  100% {
    background-image: url(https://www.mescroll.com/img/xinlang/mescroll-progress1.png);
  }
}
