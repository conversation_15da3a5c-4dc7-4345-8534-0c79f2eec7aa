/*下拉刷新--向下进度动画*/
.mescroll-downwarp .downwarp-load {
  display: inline-block;
  width: 50px;
  margin: auto;
  background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress0.png);
  background-size: 100% 100%;
  border-radius: 0;
}
/*下拉刷新--资源预加载,避免下拉的时候闪白屏*/
.mescroll-downwarp .downwarp-load-preload {
  pointer-events: none;
  animation:
    animProgress 1s steps(1, end),
    animLoading 1s steps(1, end) 1s;
}
/*下拉刷新--进度条*/
.mescroll-downwarp .downwarp-load-start {
  display: inline-block;
  width: 50px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  animation:
    animProgress 0.3s steps(1, end),
    animLoading 0.3s steps(1, end) 0.3s infinite;
}
@keyframes animProgress {
  0% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress0.png);
  }
  16% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress1.png);
  }
  32% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress2.png);
  }
  48% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress3.png);
  }
  64% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress4.png);
  }
  80% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress5.png);
  }
  100% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-progress0.png);
  }
}
@keyframes animLoading {
  0% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading1.png);
  }
  16% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading2.png);
  }
  32% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading3.png);
  }
  48% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading4.png);
  }
  64% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading5.png);
  }
  80% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading6.png);
  }
  100% {
    background-image: url(https://www.mescroll.com/img/meituan/mescroll-loading1.png);
  }
}
