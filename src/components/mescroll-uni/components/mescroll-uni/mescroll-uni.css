.mescroll-uni-warp {
  height: 100%;
}

.mescroll-uni-content {
  height: 100%;
}

.mescroll-uni {
  position: relative;
  box-sizing: border-box; /* 避免设置padding出现双滚动条的问题 */
  width: 100%;
  height: 100%;
  min-height: 200rpx;
  overflow-y: auto;
}
/* 定位的方式固定高度 */
.mescroll-uni-fixed {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: auto; /* 使right生效 */
  height: auto; /* 使bottom生效 */
}
/* 适配 iPhoneX */
@supports (bottom: constant(safe-area-inset-bottom)) or
  (bottom: env(safe-area-inset-bottom)) {
  .mescroll-safearea {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
