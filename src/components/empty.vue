<!--
 * @Author: Rain
 * @Date: 2025/07/19 22:29:05
 * @LastEditTime: 2025/07/20 13:26:21
 * @Description: 暂无内容组件
-->
<template>
  <div class="w-full h-full flex items-center justify-center flex-col">
    <image
      src="../assets/empty.webp"
      class="w-420rpx h-340rpx"
      mode="scaleToFill"
    />
    <text class="text-28rpx text-#666666 mt-10">{{ text }}</text>
  </div>
</template>

<script setup>
defineProps({
  text: {
    type: String,
    default: '暂无内容'
  }
})
onMounted(() => {
  console.log('mounted')
})
</script>
<style scoped lang="scss"></style>
