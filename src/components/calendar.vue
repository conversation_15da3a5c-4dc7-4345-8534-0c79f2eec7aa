<!--
 * @Author: Rain
 * @Date: 2025/07/21
 * @Description: 自定义日历组件
-->
<template>
  <view class="calendar-container bg-white rounded-16rpx p-32rpx box-border">
    <!-- 日历头部 -->
    <view class="calendar-header flex items-center justify-between mb-32rpx">
      <!-- 左侧导航按钮 -->
      <view class="flex items-center">
        <view class="nav-btn" @click="goToYear(-1)">
          <text class="text-24rpx text-gray-400">《</text>
        </view>
        <view class="nav-btn ml-16rpx" @click="goToMonth(-1)">
          <text class="text-24rpx text-gray-400">〈</text>
        </view>
      </view>

      <!-- 年月显示 -->
      <view class="flex items-center">
        <text class="text-32rpx font-medium text-gray-700">
          {{ currentYear }}年 {{ currentMonth }}月
        </text>
      </view>

      <!-- 右侧导航按钮 -->
      <view class="flex items-center">
        <view class="nav-btn mr-16rpx" @click="goToMonth(1)">
          <text class="text-24rpx text-gray-400">〉</text>
        </view>
        <view class="nav-btn" @click="goToYear(1)">
          <text class="text-24rpx text-gray-400">》</text>
        </view>
      </view>
    </view>

    <!-- 星期标题 -->
    <view class="weekdays-header flex mb-16rpx">
      <view
        v-for="weekday in weekdays"
        :key="weekday"
        class="weekday-cell flex items-center justify-center"
      >
        <text class="text-28rpx text-gray-500">{{ weekday }}</text>
      </view>
    </view>

    <!-- 日历网格 -->
    <view class="calendar-grid">
      <view
        v-for="week in calendarDays"
        :key="week.id"
        class="calendar-week flex"
      >
        <view
          v-for="day in week.days"
          :key="day.id"
          class="calendar-day flex items-center justify-center"
          :class="getDayClass(day)"
          @click="selectDate(day)"
        >
          <text class="day-text" :class="getDayTextClass(day)">
            {{ day.date }}
          </text>
          <!-- 小蓝点标记 -->
          <view
            v-if="day.hasEvent && !day.isToday && !day.isInRange"
            class="event-dot"
          ></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Date,
    default: () => new Date()
  },
  // 标记的日期范围 (蓝色)
  markedRanges: {
    type: Array,
    default: () => []
  },
  // 有事件的日期 (小蓝点)
  eventDates: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'dateClick'])

// 响应式数据
const currentDate = ref(new Date(props.modelValue))
const today = new Date()

// 星期标题
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 计算属性
const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonth = computed(() => currentDate.value.getMonth() + 1)

// 获取当月日历数据
const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)

  // 获取第一周的开始日期（可能是上个月的日期）
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  const weeks = []
  let weekId = 0

  // 生成6周的日历
  for (let week = 0; week < 6; week++) {
    const days = []
    for (let day = 0; day < 7; day++) {
      const currentDay = new Date(startDate)
      currentDay.setDate(startDate.getDate() + week * 7 + day)

      const dayData = {
        id: `${currentDay.getFullYear()}-${currentDay.getMonth()}-${currentDay.getDate()}`,
        date: currentDay.getDate(),
        fullDate: new Date(currentDay),
        isCurrentMonth: currentDay.getMonth() === month,
        isToday: isSameDay(currentDay, today),
        isInRange: isDateInRange(currentDay),
        hasEvent: isEventDate(currentDay)
      }

      days.push(dayData)
    }

    weeks.push({
      id: weekId++,
      days
    })
  }

  return weeks
})

// 工具函数
const isSameDay = (date1, date2) => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  )
}

const isDateInRange = (date) => {
  return props.markedRanges.some((range) => {
    const start = new Date(range.start)
    const end = new Date(range.end)
    return date >= start && date <= end
  })
}

const isEventDate = (date) => {
  return props.eventDates.some((eventDate) =>
    isSameDay(date, new Date(eventDate))
  )
}

// 获取日期样式类
const getDayClass = (day) => {
  const classes = []

  if (!day.isCurrentMonth) {
    classes.push('other-month')
  }

  if (day.isToday) {
    classes.push('today')
  } else if (day.isInRange) {
    classes.push('in-range')
  }

  return classes
}

const getDayTextClass = (day) => {
  const classes = ['text-28rpx']

  if (!day.isCurrentMonth) {
    classes.push('text-gray-300')
  } else if (day.isToday) {
    classes.push('text-white')
  } else if (day.isInRange) {
    classes.push('text-white')
  } else {
    classes.push('text-gray-700')
  }

  return classes
}

// 方法
const goToMonth = (direction) => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + direction)
  currentDate.value = newDate
  emit('change', newDate)
}

const goToYear = (direction) => {
  const newDate = new Date(currentDate.value)
  newDate.setFullYear(newDate.getFullYear() + direction)
  currentDate.value = newDate
  emit('change', newDate)
}

const selectDate = (day) => {
  if (!day.isCurrentMonth) return

  emit('update:modelValue', day.fullDate)
  emit('dateClick', day.fullDate)
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    currentDate.value = new Date(newValue)
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.calendar-container {
  width: 100%;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
    border-radius: 8rpx;
  }
}

.weekday-cell {
  width: calc(100% / 7);
  height: 60rpx;
}

.calendar-week {
  margin-bottom: 8rpx;
}

.day-text {
  font-weight: 400;
}

.calendar-day {
  position: relative;
  width: calc(100% / 7);
  height: 80rpx;
  cursor: pointer;
  border-radius: 8rpx;
  transition: all 0.2s ease;

  &:hover:not(.other-month) {
    background-color: #f0f9ff;
  }

  &.today {
    background-color: #9ca3af;

    .day-text {
      font-weight: 600;
    }
  }

  &.in-range {
    background-color: #3b82f6;

    .day-text {
      font-weight: 600;
    }
  }

  &.other-month {
    cursor: default;

    &:hover {
      background-color: transparent;
    }
  }
}

.event-dot {
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  width: 8rpx;
  height: 8rpx;
  background-color: #3b82f6;
  border-radius: 50%;
  transform: translateX(-50%);
}
</style>
