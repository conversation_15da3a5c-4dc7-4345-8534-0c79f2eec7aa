<!--
 * @Author: Rain
 * @Date: 2025/07/18 21:26:16
 * @LastEditTime: 2025/08/11 22:56:37
 * @Description: Do Something
-->
<template>
  <Mescroll
    ref="mescrollRef"
    top="0"
    bottom="0"
    style="height: 100%"
    :bottombar="false"
    :safearea="true"
    :fixed="false"
    @init="mescrollInit"
    @up="upCallback"
    :up="{
      auto: true,
      page: {
        num: 0,
        size: 10 // 每页数据的数量,默认10
      },
      textLoading: '--加载中--',
      textNoMore: '--我们也是有底线的--',
      noMoreSize: 5,
      empty: {
        title: '',
        icon: 'https://lybsc.njyanyu.com/uploads/empty.png'
      }
    }"
    :down="{
      use: false
    }"
  >
    <view
      v-for="(item, index) in dataList"
      :key="index"
      class="w-full overflow-hidden"
    >
      <slot :item="item"></slot>
    </view>
  </Mescroll>
</template>

<script setup>
import Mescroll from '@/components/mescroll-uni/components/mescroll-uni/mescroll-uni.vue'
import useMescroll from '@/components/mescroll-uni/hooks/useMescroll'
const { mescrollInit } = useMescroll()

const page = ref(0)
const props = defineProps({
  request: {
    type: Function
  },
  queryParams: {
    type: Object,
    default: () => ({
      pageSize: 10,
      pageNum: 1
    })
  }
})
const dataList = ref([])
const upCallback = (mescroll) => {
  page.value = mescroll.num
  getList(mescroll)
}
const getList = (mescroll) => {
  if (!props.request) {
    return
  }
  props
    .request({
      pageNum: page.value,
      ...props.queryParams
    })
    .then((res) => {
      const { total, list } = res
      if (page.value === 1) {
        dataList.value = list
      } else {
        dataList.value = dataList.value.concat(list)
      }
      mescroll.endBySize(dataList.value.length, total)
    })
    .catch(() => {
      mescroll.endUpScroll(true)
      mescroll.endErr()
    })
}
defineExpose({
  query: getList
})
</script>
