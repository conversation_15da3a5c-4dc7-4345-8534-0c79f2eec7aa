<!--
 * @Author: Rain
 * @Date: 2025/07/21 12:18:28
 * @LastEditTime: 2025/07/21 14:17:33
 * @Description: 打卡提醒设置
-->
<template>
  <view class="w-full flex flex-col p-30 box-border bg-white p-[60rpx_30rpx]">
    <view class="w-full flex items-center justify-between mt-60">
      <text class="text-#333333 text-28rpx font-600">开启打卡提醒</text>
      <view class="flex items-center text-#999 text-24rpx">
        每天<text class="text-#2D8FF0">9:00</text>发送提醒
        <wd-switch v-model="checked" custom-class="ml-10!" :size="24" />
      </view>
    </view>
    <view class="text-#999999 text-28rpx font-500 mt-30"
      >打卡提醒通过“1998打卡”公众号发送，开启后可接收每日打卡提醒。</view
    >
  </view>
</template>

<script setup>
const checked = ref(false)
onMounted(() => {
  console.log('mounted')
})
</script>
<style scoped lang="scss"></style>
<route lang="json">
{
  "style": { "navigationBarTitleText": "打卡提醒设置" }
}
</route>
