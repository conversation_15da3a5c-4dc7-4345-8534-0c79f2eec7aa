<!--
 * @Author: Rain
 * @Date: 2025/07/21
 * @Description: 我的打卡记录
-->
<template>
  <view class="clock-record-page bg-white w-full h-full">
    <!-- 统计信息卡片 -->
    <view class="stats-card bg-white rounded-16rpx p-20rpx box-border">
      <view class="flex items-center justify-between">
        <view class="stat-item text-center">
          <view
            view
            class="stat-number text-72rpx font-bold text-#333333 flex items-center justify-center"
          >
            <text>{{ totalDays }} </text>
            <text class="text-32rpx">天</text>
          </view>
          <text class="stat-label text-28rpx text-#999999 block mt-8rpx"
            >累计打卡</text
          >
        </view>
        <view class="stat-item text-center">
          <view
            class="stat-number text-72rpx font-bold text-#333333 flex items-center justify-center"
          >
            <text>{{ continuousDays }}</text>
            <text class="text-32rpx">天</text>
          </view>
          <text class="stat-label text-28rpx text-#999999 block mt-8rpx"
            >最长连续打卡</text
          >
        </view>
      </view>
    </view>

    <!-- 自定义日历组件 -->
    <Calendar
      v-model="selectedDate"
      :marked-ranges="markedRanges"
      :event-dates="eventDates"
      @change="handleMonthChange"
      @date-click="handleDateClick"
    />

    <!-- 查看全部按钮 -->
    <view
      class="view-all-btn flex items-center justify-between p-20 bg-white"
      @click="goToAllRecords"
    >
      <text class="text-28rpx font-500 text-blue-500">查看全部</text>
    </view>

    <!-- 用户信息和分享卡片 -->
    <view class="user-card bg-white rounded-16rpx p-32rpx mt-32rpx">
      <view class="flex items-center justify-between mb-24rpx">
        <view class="flex items-center">
          <image
            class="avatar w-80rpx h-80rpx rounded-full mr-24rpx"
            :src="userInfo.avatar"
            mode="aspectFill"
          />
          <view>
            <text class="text-28rpx font-medium text-gray-800 block">{{
              userInfo.name
            }}</text>
            <text class="text-24rpx text-gray-500"
              >已坚持{{ userInfo.persistDays }}天</text
            >
          </view>
        </view>
        <view
          class="share-btn px-24rpx py-12rpx bg-blue-50 rounded-12rpx"
          @click="shareRecord"
        >
          <text class="text-24rpx text-blue-500">分享海报</text>
        </view>
      </view>

      <!-- 分享内容 -->
      <view class="share-content">
        <text class="text-28rpx text-gray-700 leading-relaxed">
          {{ shareText }}
        </text>
        <text class="text-24rpx text-blue-500 ml-8rpx" @click="expandText"
          >展开</text
        >
      </view>

      <!-- 分享图片 -->
      <view class="share-images flex mt-24rpx" v-if="shareImages.length > 0">
        <image
          v-for="(img, index) in shareImages"
          :key="index"
          class="share-img w-160rpx h-160rpx rounded-12rpx mr-16rpx"
          :src="img"
          mode="aspectFill"
          @click="previewImage(img, shareImages)"
        />
      </view>

      <!-- 时间和任务信息 -->
      <view
        class="task-info flex items-center justify-between mt-24rpx pt-24rpx border-t border-gray-100"
      >
        <text class="text-24rpx text-gray-400">{{
          formatDate(selectedRecord.date)
        }}</text>
        <view class="flex items-center">
          <view
            class="task-tag flex items-center bg-pink-50 px-16rpx py-8rpx rounded-12rpx mr-16rpx"
          >
            <text class="task-emoji mr-8rpx">🌸</text>
            <text class="text-20rpx text-pink-600">{{
              selectedRecord.taskName
            }}</text>
          </view>
        </view>
      </view>

      <!-- 互动统计 -->
      <view
        class="interaction-stats flex items-center justify-between mt-24rpx"
      >
        <view class="stat-item flex items-center">
          <text class="icon text-24rpx mr-8rpx">👍</text>
          <text class="text-24rpx text-gray-500">{{
            selectedRecord.likes || 0
          }}</text>
        </view>
        <view class="stat-item flex items-center">
          <text class="icon text-24rpx mr-8rpx">💬</text>
          <text class="text-24rpx text-gray-500">{{
            selectedRecord.comments || 499
          }}</text>
        </view>
        <view class="stat-item flex items-center">
          <text class="icon text-24rpx mr-8rpx">❤️</text>
          <text class="text-24rpx text-gray-500">{{
            selectedRecord.hearts || 499
          }}</text>
        </view>
        <view class="stat-item flex items-center">
          <text class="icon text-24rpx mr-8rpx">📤</text>
          <text class="text-24rpx text-gray-500">分享</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 响应式数据
const selectedDate = ref(new Date())
const totalDays = ref(20)
const continuousDays = ref(20)

// 用户信息
const userInfo = ref({
  name: '张小美',
  avatar: '/static/logo.png',
  persistDays: 20
})

// 分享文本
const shareText = ref(
  '好的界面设计并不始于图片，而是始于对于人的理解，比如人们喜欢什么，为什么人们会试用某种特定的软件，他们可能与之怎样交互...'
)

// 分享图片 - 使用现有资源作为占位符
const shareImages = ref([
  '/static/logo.png',
  '/static/tabbar/home_active.png',
  '/static/tabbar/my_active.png'
])

// 模拟的打卡数据
const clockRecords = ref([
  {
    id: 1,
    date: '2024-02-13',
    taskName: '这里是一个任务名称',
    time: '08:30',
    completed: true,
    likes: 0,
    comments: 499,
    hearts: 499
  },
  {
    id: 2,
    date: '2024-02-16',
    taskName: '英语阅读',
    time: '09:15',
    completed: true,
    likes: 5,
    comments: 12,
    hearts: 8
  },
  {
    id: 3,
    date: '2024-02-20',
    taskName: '健身训练',
    time: '19:00',
    completed: true,
    likes: 3,
    comments: 6,
    hearts: 15
  },
  {
    id: 4,
    date: '2024-02-22',
    taskName: '跑步',
    time: '07:00',
    completed: true,
    likes: 8,
    comments: 3,
    hearts: 12
  }
])

// 标记的时间范围（蓝色显示）
const markedRanges = ref([
  {
    start: '2024-02-13',
    end: '2024-02-16'
  }
])

// 有事件的日期（显示小蓝点）
const eventDates = computed(() => {
  return clockRecords.value.map((record) => record.date)
})

// 选中日期的记录
const selectedRecord = computed(() => {
  const dateStr = formatDateToString(selectedDate.value)
  const record = clockRecords.value.find((record) => record.date === dateStr)
  return (
    record || {
      id: 0,
      date: dateStr,
      taskName: '这里是一个任务名称',
      time: '00:00',
      completed: false,
      likes: 0,
      comments: 499,
      hearts: 499
    }
  )
})

// 工具函数
const formatDateToString = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}.${month}.${day}`
}

// 事件处理
const handleMonthChange = (date) => {
  console.log('月份切换:', date)
}

const handleDateClick = (date) => {
  selectedDate.value = date
  console.log('选择日期:', formatDateToString(date))
}

const goToAllRecords = () => {
  uni.navigateTo({
    url: '/sub-pages/clock-dynamic/index'
  })
}

const shareRecord = () => {
  uni.showActionSheet({
    itemList: ['生成海报', '分享到微信', '分享到朋友圈'],
    success: (res) => {
      console.log('选中了第' + (res.tapIndex + 1) + '个按钮')
      switch (res.tapIndex) {
        case 0:
          generatePoster()
          break
        case 1:
          shareToWeChat()
          break
        case 2:
          shareToMoments()
          break
      }
    }
  })
}

const generatePoster = () => {
  uni.showToast({
    title: '正在生成海报...',
    icon: 'loading',
    duration: 2000
  })
}

const shareToWeChat = () => {
  uni.showToast({
    title: '分享到微信',
    icon: 'success'
  })
}

const shareToMoments = () => {
  uni.showToast({
    title: '分享到朋友圈',
    icon: 'success'
  })
}

const expandText = () => {
  uni.showModal({
    title: '完整内容',
    content:
      shareText.value + '这是展开后的完整内容，用户可以看到更多详细信息。',
    showCancel: false
  })
}

const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}

onMounted(() => {
  console.log('打卡记录页面加载完成')
  // 模拟加载数据
  loadUserData()
})

const loadUserData = async () => {
  try {
    // 这里可以调用API获取用户数据
    console.log('加载用户打卡数据')
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}
</script>

<style scoped lang="scss">
.clock-record-page {
  box-sizing: border-box;
  padding: 32rpx;
}

.stats-card {
  .stat-item {
    flex: 1;
  }

  .stat-number {
    line-height: 1.2;
  }
}

.view-all-btn {
  transition: all 0.2s ease;

  &:active {
    background-color: #f3f4f6;
  }
}

.user-card {
  .avatar {
    object-fit: cover;
  }

  .share-btn {
    transition: all 0.2s ease;

    &:active {
      background-color: #dbeafe;
    }
  }

  .share-content {
    line-height: 1.6;
  }

  .share-images {
    .share-img {
      object-fit: cover;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .task-tag {
    .task-emoji {
      font-size: 20rpx;
    }
  }

  .interaction-stats {
    .stat-item {
      .icon {
        opacity: 0.8;
      }
    }
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .share-images {
    .share-img {
      width: 120rpx;
      height: 120rpx;
    }
  }
}
</style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "我的打卡记录",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>
