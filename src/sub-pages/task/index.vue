<!--
 * @Author: Rain
 * @Date: 2025/07/19 22:51:30
 * @LastEditTime: 2025/08/10 10:12:31
 * @Description: Do Something
-->
<template>
  <scroll-view
    class="w-full h-full bg-white flex flex-col items-center"
    :enable-flex="true"
    :scroll-y="true"
  >
    <image
      class="w-full h-400rpx object-cover min-h-400rpx"
      :src="taskData.coverImage"
      mode="aspectFill"
    />
    <view class="w-full flex flex-col p-20 box-border">
      <!-- 任务基本信息 -->
      <view class="text-36rpx font-500 mb-20 text-#333333">{{
        taskData.title
      }}</view>
      <view class="flex items-center">
        <text class="text-24rpx text-#666666">{{ taskData.dateRange }}</text>
        <view class="flex items-center">
          <text class="text-24rpx opacity-90 mr-16rpx">{{
            taskData.status
          }}</text>
        </view>
      </view>
      <view class="flex items-center mt-16rpx text-#999999">
        <view class="text-24rpx pr-20rpx mr-20 b-r b-r-solid b-r-#999999"
          >已有{{ taskData.participantCount }}人参与</view
        >
        <text class="text-24rpx">{{ taskData.checkInCount }}次打卡</text>
        <view class="flex-1 flex justify-end">
          <text class="text-24rpx opacity-90">我的打卡记录</text>
        </view>
      </view>
    </view>

    <!-- 进入打卡按钮 -->
    <view class="w-full min-h-80rpx flex items-center justify-center my-30">
      <wd-button type="primary" round @click="joinTask" custom-class="w-80%">
        进入打卡
      </wd-button>
    </view>
    <!-- Tab切换 -->
    <view
      class="flex w-full items-center px-20 box-border b-b-solid b-b-1 b-b-[#dedede] pb-20"
    >
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="transition-all flex flex-col items-center mr-70"
        :class="activeTab === index ? 'text-#2D8FF0' : 'text-#666666'"
        @tap="activeTab = index"
      >
        <text class="text-28rpx font-600">{{ tab }}</text>
        <view
          class="w-12rpx h-12rpx bg-[#2D8FF0] rounded-[50%] mt-8rpx"
          :class="activeTab === index ? 'opacity-100' : 'opacity-0'"
        />
      </view>
    </view>
    <!-- 内容区域 -->
    <view class="px-30rpx py-24rpx box-border w-full">
      <!-- 打卡介绍 -->
      <CheckInIntro
        v-if="activeTab === 0"
        :teacher-info="taskData.teacherInfo"
        :description="taskData.description"
      />
      <!-- 任务列表 -->
      <TaskList v-if="activeTab === 1" :tasks="taskData.tasks" />
    </view>
  </scroll-view>
</template>

<script setup>
// 当前激活的tab
const activeTab = ref(0)

// Tab选项
const tabs = ['打卡介绍', '任务列表']

// 任务数据
const taskData = ref({
  title: '考公每日练习',
  dateRange: '2025.06.06-2025.06.30',
  status: '进行中',
  participantCount: 20,
  checkInCount: 100,
  coverImage:
    'https://via.placeholder.com/750x400/FFB6C1/FFFFFF?text=考公每日练习',
  teacherInfo: {
    name: '张三老师',
    avatar: 'https://via.placeholder.com/80x80/4A90E2/FFFFFF?text=张',
    checkInCount: 0
  },
  description: ``,
  tasks: [
    {
      id: 1,
      title: '每日阅读练习',
      description: '阅读指定文章并完成理解题目',
      status: '进行中',
      progress: 75
    },
    {
      id: 2,
      title: '逻辑推理训练',
      description: '完成逻辑推理题目练习',
      status: '未开始',
      progress: 0
    }
  ]
})

// 加入打卡
const joinTask = () => {
  uni.navigateTo({
    url: '/sub-pages/checkin/index'
  })
}
</script>

<style lang="scss" scoped>
// 自定义样式
</style>
<route lang="json">
{
  "style": {
    "navigationBarTitleText": "打卡"
  }
}
</route>
