<!--
 * @Author: Rain
 * @Date: 2025/07/20 15:30:00
 * @Description: 任务列表组件
-->
<template>
  <view class="w-full h-full">
    <wd-steps :active="0" vertical dot class="w-full">
      <wd-step
        title="注册1个账号"
        description="2027-07-10"
        v-for="(item, index) in tasks"
        :key="index"
        @tap="getDetail"
      />
    </wd-steps>
  </view>
</template>

<script setup>
// 定义props
defineProps({
  tasks: {
    type: Array,
    default: () => []
  }
})

// // 获取状态样式类
// const getStatusClass = (status) => {
//   switch (status) {
//     case '进行中':
//       return 'bg-blue-100 text-blue-600'
//     case '未开始':
//       return 'bg-gray-100 text-gray-600'
//     case '已完成':
//       return 'bg-green-100 text-green-600'
//     default:
//       return 'bg-gray-100 text-gray-600'
//   }
// }

const getDetail = () => {
  uni.navigateTo({
    url: '/sub-pages/task-detail/index'
  })
}
</script>

<style lang="scss" scoped></style>
