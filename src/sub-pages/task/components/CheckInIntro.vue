<!--
 * @Author: Rain
 * @Date: 2025/07/20 15:30:00
 * @Description: 打卡介绍组件
-->
<template>
  <view class="bg-white rounded-16rpx w-full">
    <!-- 老师信息 -->
    <view class="mb-32rpx">
      <text class="text-32rpx font-bold text-gray-900 mb-24rpx block"
        >老师</text
      >
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <image
            class="w-80rpx h-80rpx rounded-full mr-24rpx"
            :src="teacherInfo.avatar"
            mode="aspectFill"
          />
          <text class="text-28rpx font-medium text-#666666">{{
            teacherInfo.name
          }}</text>
        </view>

        <view class="flex items-center text-#666666">
          <text class="text-24rpx mr-8rpx">他创建的打卡</text>
          <text class="text-24rpx"></text>
        </view>
      </view>
    </view>

    <!-- 打卡介绍标题 -->
    <view class="mb-24rpx">
      <text class="text-32rpx font-bold text-gray-900">打卡介绍</text>
    </view>

    <!-- 介绍内容 -->
    <view class="text-28rpx text-gray-700 leading-loose">
      <text>{{ description }}</text>
    </view>
  </view>
</template>

<script setup>
// 定义props
defineProps({
  teacherInfo: {
    type: Object,
    required: true
  },
  description: {
    type: String,
    required: true
  }
})
</script>

<style lang="scss" scoped>
// 组件样式
</style>
