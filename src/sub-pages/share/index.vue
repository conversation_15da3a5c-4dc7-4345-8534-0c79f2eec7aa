<!--
 * @Author: Rain
 * @Date: 2025/07/19 21:54:29
 * @LastEditTime: 2025/07/19 22:25:30
 * @Description: 打卡分享页面
-->
<template>
  <view
    class="w-full h-full overflow-hidden bg-[#f6f6f6] flex flex-col pb-120rpx box-border"
  >
    <!-- 主要分享卡片 -->
    <view class="flex-1 w-full flex flex-col">
      <!-- 背景图片区域 -->
      <view class="relative w-full overflow-hidden h-full">
        <!-- 云海背景图片占位符 -->
        <image
          class="w-full h-full object-cover"
          :src="shareData.backgroundImage || ''"
          mode="aspectFill"
        >
        </image>

        <!-- 日期信息 -->
        <view class="absolute top-8 left-6">
          <text class="text-white text-6xl font-bold mb-2 block">{{
            shareData.day
          }}</text>
          <text class="text-white text-lg opacity-90">{{
            shareData.dateText
          }}</text>
        </view>

        <!-- 打卡信息 -->
        <view class="absolute bottom-8 left-6 right-6">
          <text class="text-white text-lg mb-4 block"
            >我加入《{{ shareData.activityName }}》</text
          >
          <view class="flex items-baseline mb-2">
            <text class="text-white text-base mr-2">连续打卡</text>
            <text class="text-white text-5xl font-bold mr-2">{{
              shareData.consecutiveDays
            }}</text>
            <text class="text-white text-base">天</text>
          </view>
          <text class="text-white text-sm opacity-80">
            累计打卡{{ shareData.totalDays }}天 最长连续打卡{{
              shareData.maxConsecutiveDays
            }}天
          </text>
        </view>
      </view>

      <!-- 底部用户信息区域 -->
      <view class="bg-white p-20 box-border flex items-center justify-between">
        <view class="flex-1 overflow-hidden flex flex-col">
          <view class="flex items-center flex-1 overflow-hidden">
            <image
              class="w-12 h-12 rounded-full mr-20"
              :src="shareData.userAvatar || ''"
              mode="aspectFill"
            ></image>
            <text class="text-gray-900 text-base font-medium block mb-1">{{
              shareData.userName
            }}</text>
          </view>
          <view class="w-full flex flex-col">
            <text class="text-#333333 text-28rpx mb-10">{{
              shareData.userStats
            }}</text>
            <text class="text-#999999 text-28rpx">{{
              shareData.qrCodeText
            }}</text>
          </view>
        </view>
        <image
          class="w-16 h-16 rounded-2"
          :src="shareData.qrCode || ''"
          mode="aspectFit"
        >
        </image>
      </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="box-border mt-40rpx flex items-center w-full justify-around">
      <wd-button
        custom-class="bg-white! text-gray-700! border-none!"
        :round="false"
        @click="saveImage"
      >
        保存图片
      </wd-button>
      <wd-button
        type="primary"
        open-type="share"
        @click="shareToFriends"
        :round="false"
      >
        分享
      </wd-button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 分享数据
const shareData = ref({
  day: '09',
  dateText: '八月 星期六',
  activityName: '每日读口语',
  consecutiveDays: 999,
  totalDays: 999,
  maxConsecutiveDays: 999,
  userName: '这里是一个名字',
  userStats: '1998打卡',
  qrCodeText: '长按识别小程序码',
  backgroundImage: '', // 云海背景图片
  userAvatar: '', // 用户头像
  qrCode: '' // 小程序码
})

// 保存图片
const saveImage = () => {
  uni.showToast({
    title: '保存成功',
    icon: 'success'
  })
}

// 分享给朋友
const shareToFriends = () => {
  uni.showShareMenu({
    withShareTicket: true,
    success: () => {
      console.log('分享成功')
    },
    fail: (err) => {
      console.log('分享失败', err)
    }
  })
}
</script>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "分享"
  }
}
</route>
