<!--
 * @Author: Rain
 * @Date: 2025/07/21 14:21:41
 * @LastEditTime: 2025/07/21 14:39:06
 * @Description: 老师创建的打卡记录
-->
<template>
  <view class="w-full h-full overflow-hidden p-20 box-border bg-white">
    <MescrollList>
      <view
        v-for="(task, index) in list"
        :key="index"
        :task="task"
        class="w-full flex flex-col"
      >
        <view class="w-full flex">
          <image src="" mode="scaleToFill" class="w-160rpx h-120rpx mr-20" />
          <view class="flex-1 flex flex-col">
            <view class="text-#333 text-28rpx font-600 mb-10">英语阅读</view>
            <text class="text-#666666 text-28rpx">练习模式</text>
          </view>
          <wd-icon name="jump" size="22px" color="#666666"></wd-icon>
        </view>
        <view
          class="flex items-center justify-between text-24rpx text-#333333 h-66rpx min-h-66rpx bg-[#eff8ff] p-20 box-border"
        >
          <view class="flex items-center">2025.06.01到2026.06.3 </view>
          <view class="flex items-center">已有10000人参与 </view>
        </view>
      </view>
    </MescrollList>
  </view>
</template>

<script setup>
onMounted(() => {
  console.log('mounted')
})
const list = ref([
  {
    category: '英语阅读',
    title: '每日英语阅读',
    status: 0
  }
])
</script>
<style scoped lang="scss"></style>

<route type="home" lang="json">
{
  "style": {
    "navigationBarTitleText": "张老师"
  }
}
</route>
