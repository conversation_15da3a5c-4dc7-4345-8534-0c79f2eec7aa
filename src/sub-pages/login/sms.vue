<!--
 * @Author: Rain
 * @Date: 2025/07/18 22:19:22
 * @LastEditTime: 2025/08/10 13:35:17
 * @Description: Do Something
-->
<template>
  <view
    class="w-full h-full bg-white flex flex-col overflow-hidden p-[60rpx_40rpx] box-border relative"
  >
    <!-- Phone number input -->
    <view class="w-full flex items-center py-20 border-b border-gray-200 mb-4">
      <wd-input
        type="tel"
        v-model="phone"
        placeholder="请输入手机号"
        :maxlength="11"
        :border="false"
        custom-class="flex-1 text-lg"
      />
    </view>

    <!-- Verification code input -->
    <view class="w-full flex items-center py-20 border-b border-gray-200">
      <wd-input
        type="number"
        v-model="code"
        placeholder="请输入验证码"
        :maxlength="5"
        :border="false"
        custom-class="flex-1 text-lg"
      >
        <template #suffix>
          <wd-button
            type="info"
            size="small"
            :disabled="!isPhoneValid || countdown > 0"
            @click="sendCode"
            :plain="!isPhoneValid || countdown === 0"
          >
            {{ countdown > 0 ? `剩余${countdown}秒` : '获取验证码' }}
          </wd-button>
        </template>
      </wd-input>
    </view>

    <!-- Agreement checkbox -->
    <view class="w-full flex items-center my-30">
      <wd-checkbox v-model="checked" shape="square">
        <view class="flex items-center text-sm flex-wrap">
          <text class="text-gray-500">我已阅读并同意</text>
          <text class="text-gray-500" @click.stop="viewAgreement"
            >《用户协议》</text
          >
          <text class="text-gray-500">和</text>
          <text class="text-gray-500" @click.stop="viewPrivacy"
            >《隐私政策》</text
          >
        </view>
      </wd-checkbox>
    </view>

    <!-- Login button -->
    <wd-button
      :disabled="!isFormValid"
      block
      size="large"
      :custom-class="[
        'mt-10',
        'outline-none',
        !isFormValid ? 'bg-[#cccccc]!' : ''
      ]"
      @click="login"
    >
      登录
    </wd-button>

    <!-- Other login method -->
    <view class="w-full text-center mt-30 flex justify-center">
      <wd-button
        type="info"
        open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
        custom-class="w-80% bg-#fff! border-none outline-none"
        >手机号快捷登录</wd-button
      >
    </view>
    <verify v-model="show"></verify>
  </view>
</template>

<script setup>
import verify from './verify.vue'

const phone = ref('')
const code = ref('')
const checked = ref(true)
const countdown = ref(0)
let timer = null
const show = ref(true)

const isPhoneValid = computed(() =>
  /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/.test(
    phone.value
  )
)

const isFormValid = computed(
  () => isPhoneValid.value && code.value.length === 5 && checked.value
)

const startCountdown = () => {
  show.value = true
  if (countdown.value > 0) return
  countdown.value = 60
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      timer = null
    }
  }, 1000)
}

const sendCode = () => {
  show.value = true
  if (!isPhoneValid.value) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return
  }
  if (countdown.value > 0) return
  // TODO: send code API call
  uni.showToast({ title: '验证码已发送', icon: 'none' })
  startCountdown()
}

const login = () => {
  if (!isFormValid.value) {
    let title = '请完成表单'
    if (!isPhoneValid.value) {
      title = '请输入正确的手机号'
    } else if (code.value.length !== 5) {
      title = '请输入5位验证码'
    } else if (!checked.value) {
      title = '请先同意用户协议和隐私政策'
    }
    uni.showToast({ title, icon: 'none' })
    return
  }
  // TODO: login request
  uni.showToast({
    title: '登录成功',
    icon: 'success'
  })
}

const viewAgreement = () => {
  // TODO: Navigate to user agreement page
  uni.showToast({ title: '查看用户协议', icon: 'none' })
}

const viewPrivacy = () => {
  // TODO: Navigate to privacy policy page
  uni.showToast({ title: '查看隐私政策', icon: 'none' })
}

const getPhoneNumber = (e) => {
  console.log(e)
}
</script>

<style scoped lang="scss">
:deep(.wd-input) {
  padding: 0 !important;
}
:deep(.wd-button--text) {
  color: #409eff;
}
:deep(.wd-button.is-disabled) {
  color: #c0c4cc;
}
</style>

<route lang="json">
{
  "style": { "navigationBarTitleText": "登录" },
  "name": "smsLogin"
}
</route>
