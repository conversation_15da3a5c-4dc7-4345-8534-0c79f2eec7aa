<!--
 * @Author: Rain
 * @Date: 2025/08/10 13:05:47
 * @LastEditTime: 2025/08/11 12:13:31
 * @Description: Do Something
-->
<template>
  <view
    class="w-full h-full absolute top-0 left-0 bg-[rgba(0,0,0,0.5)] p-20 box-border"
    v-if="model"
  >
    <web-view
      src="https://www.service.1998xuexi.com/slider/tac.html?phoneNum=13675134228&sendType=3"
      @onPostMessage="onMessage"
      @message="onMessage"
    />
  </view>
</template>

<script setup>
console.log(0)
const model = defineModel({ type: Boolean, default: false })
const onMessage = (event) => {
  console.log(event)
}
</script>

<style lang="scss" scoped></style>
