<!--
 * @Author: Rain
 * @Date: 2025/07/15 09:16:11
 * @LastEditTime: 2025/08/11 13:59:05
 * @Description: Do Something
-->
<template>
  <view
    class="bg-white h-full overflow-hidden flex flex-col items-center justify-center"
  >
    <!-- Logo -->
    <image
      src="../../assets/login/logo.png"
      mode="scaleToFill"
      class="w-230rpx h-230rpx"
      style="margin-top: -200rpx"
    />
    <!-- Login Buttons -->
    <view class="flex flex-col items-center w-full mt-20 px-10">
      <wd-button
        type="primary"
        block
        round
        size="large"
        custom-class="w-80% text-32rpx! font-500! mt-130rpx! mb-30rpx!"
        open-type="getPhoneNumber"
        @getphonenumber="getPhoneNumber"
        >手机号快捷登录</wd-button
      >
      <view class="mt-4 text-#979797 text-28rpx" @tap="doLogin"
        >使用其他方式登录</view
      >
    </view>

    <!-- Agreement -->
    <view class="absolute bottom-10 flex items-center">
      <wd-checkbox v-model="agreed" />
      <view class="text-xs text-gray-500 ml-1">
        我已阅读并同意
        <text class="text-[#409EFF]">《用户协议》</text>
        <text class="text-[#409EFF]">《隐私政策》</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

const agreed = ref(true)

const getPhoneNumber = (e) => {
  const { encryptedData, iv } = e
  userStore.wxLoginAction({ encryptedData, iv }).then(() => {
    uni.navigateBack()
  })
}
const doLogin = () => {
  uni.navigateTo({
    url: '/sub-pages/login/sms'
  })
}
</script>

<route lang="json">
{
  "style": { "navigationBarTitleText": "登录" },
  "name": "Login"
}
</route>
