<!--
 * @Author: Rain
 * @Date: 2025/07/20 16:50:00
 * @Description: 任务详情页面
-->
<template>
  <scroll-view
    class="w-full h-full bg-#f6f6f6 flex flex-col"
    :enable-flex="true"
    :scroll-y="true"
  >
    <!-- 任务头部信息 -->
    <view class="w-full bg-white">
      <!-- 任务标题和时间 -->
      <view class="px-32rpx pt-32rpx pb-24rpx">
        <view class="text-36rpx font-bold text-#333333 mb-16rpx">
          {{ taskData.title }}
        </view>
        <view class="text-28rpx text-#666666 mb-24rpx">
          {{ taskData.dateRange }}
        </view>

        <!-- 任务描述 -->
        <view class="text-28rpx text-#333333 leading-relaxed mb-24rpx">
          {{ taskData.description }}
        </view>
      </view>

      <!-- 任务封面图片 -->
      <view class="px-32rpx mb-24rpx">
        <image
          class="w-full h-400rpx rounded-16rpx object-cover"
          :src="taskData.coverImage"
          mode="aspectFill"
          @error="handleImageError"
        />
      </view>

      <!-- 任务详细描述 -->
      <view class="px-32rpx mb-32rpx box-border">
        <view
          class="text-28rpx text-#333333 leading-relaxed"
          :class="[!isDescriptionExpanded ? 'line-clamp-3' : '']"
        >
          {{ taskData.detailDescription }}
        </view>

        <!-- 收起/展开按钮 -->
        <view
          class="flex items-center justify-center mt-16rpx"
          @tap="toggleDescription"
        >
          <text class="text-28rpx text-#2D8FF0">
            {{ isDescriptionExpanded ? '收起' : '展开' }}
          </text>
          <text class="text-24rpx text-#2D8FF0 ml-8rpx">
            {{ isDescriptionExpanded ? '▲' : '▼' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 立即打卡按钮 -->
    <view class="px-32rpx pb-32rpx box-border bg-white">
      <wd-button
        type="primary"
        round
        block
        :loading="joinLoading"
        @click="handleJoinTask"
        custom-class="h-88rpx text-32rpx"
      >
        {{ userJoined ? '立即打卡' : '立即打卡' }}
      </wd-button>
    </view>

    <!-- 打卡动态标题 -->
    <view class="px-20 py-20 box-border flex items-center justify-between">
      <text class="text-28rpx font-600 text-#333333">打卡动态</text>
      <text class="text-28rpx text-#666666"
        >共{{ taskData.totalCheckIns }}条动态</text
      >
    </view>

    <!-- 打卡动态列表 -->
    <view class="px-32rpx w-full bg-#f6f6f6 box-border">
      <view
        v-for="item in checkInList"
        :key="item.id"
        class="mb-32rpx bg-white p-20 box-border rounded-20rpx"
      >
        <!-- 用户信息 -->
        <view class="flex items-center mb-16rpx">
          <image
            class="w-80rpx h-80rpx rounded-full mr-24rpx"
            :src="item.user.avatar"
            mode="aspectFill"
          />
          <view class="flex flex-1 flex-col">
            <text class="text-28rpx font-600 text-#333333 mr-16rpx">
              {{ item.user.name }}
            </text>
            <text class="text-24rpx text-#666666">
              已坚持{{ item.user.checkInDays }}天
            </text>
          </view>

          <!-- 分享按钮 -->
          <wd-button
            type="primary"
            plain
            custom-class="rounded-10rpx! px-20! h-32px! min-w-60rpx!"
            >分享海报</wd-button
          >
        </view>

        <!-- 打卡内容 -->
        <view class="text-28rpx text-#333333 leading-relaxed mb-16rpx">
          {{ item.content }}
        </view>

        <!-- 打卡图片 -->
        <view
          v-if="item.images && item.images.length > 0"
          class="flex flex-wrap gap-16rpx mb-16rpx"
        >
          <image
            v-for="(img, imgIndex) in item.images.slice(0, 3)"
            :key="imgIndex"
            class="w-200rpx h-200rpx rounded-12rpx object-cover"
            :src="img"
            mode="aspectFill"
            @tap="previewImage(item.images, imgIndex)"
          />
        </view>

        <!-- 时间 -->
        <view class="text-24rpx text-#999999 mb-16rpx">
          {{ item.createdAt }}
        </view>

        <view class="w-ful flex items-center bg-#f6f6f6 mb-16rpx">
          <image src="" mode="scaleToFill" class="w-104rpx h-80rpx mr-20rpx" />
          <text class="text-#666666 text-28rpx">任务名称</text>
        </view>
        <!-- 互动区域 -->
        <view class="flex items-center w-full justify-around">
          <!-- 点赞 -->
          <view class="flex items-center mr-32rpx" @tap="toggleLike(item)">
            <text
              class="text-24rpx mr-8rpx"
              :class="item.isLiked ? 'text-#ff4757' : 'text-#999999'"
            >
              {{ item.isLiked ? '❤️' : '🤍' }}
            </text>
            <text class="text-24rpx text-#999999">{{ item.likeCount }}</text>
          </view>

          <!-- 评论 -->
          <view class="flex items-center mr-32rpx">
            <text class="text-24rpx text-#999999 mr-8rpx">💬</text>
            <text class="text-24rpx text-#999999">{{ item.commentCount }}</text>
          </view>

          <!-- 分享 -->
          <view class="flex items-center">
            <wd-icon name="share" size="16px" color="#666666"></wd-icon>
            <text class="text-24rpx text-#999999">{{ item.shareCount }}</text>
          </view>

          <view class="p-8rpx" @tap="deleteCheckIn(item)">
            <wd-icon name="delete-thin" size="16px" color="#666666"></wd-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore" class="flex items-center justify-center py-32rpx">
      <wd-loading v-if="loading" />
      <text v-else class="text-28rpx text-#666666">加载更多</text>
    </view>
  </scroll-view>
</template>

<script setup>
// 页面状态
const loading = ref(false)
const joinLoading = ref(false)
const hasMore = ref(true)
const isDescriptionExpanded = ref(false)
const userJoined = ref(false)

// 任务数据
const taskData = ref({
  id: 1,
  title: '大作文四-实干精神练习',
  dateRange: '2025-06-23 打卡任务',
  description:
    '首善庄周梦为胡蝶，胡蝶然胡蝶也，自喻适志与！不知周也，俄然觉，则蘧蘧然周也。不知周之梦为胡蝶与，胡蝶之梦为周与？周与胡蝶，则必有分矣。此之谓物化。',
  detailDescription:
    '周与胡蝶则必有分矣，此之谓物化。首善庄周梦为胡蝶，胡蝶然胡蝶也，自喻适志与！不知周也，俄然觉，则蘧蘧然周也。不知周之梦为胡蝶与，胡蝶之梦为周与？周与胡蝶则必有分矣，此之谓物化。首善庄周梦为胡蝶，胡蝶然胡蝶也，自喻适志与！不知周也，俄然觉，则蘧蘧然周也。不知周之梦为胡蝶与，胡蝶之梦为周与？周与胡蝶则必有分矣，此之谓物化。',
  coverImage: 'https://via.placeholder.com/750x400/FFB6C1/FFFFFF?text=任务封面',
  totalCheckIns: 20,
  userJoined: false
})

// 打卡动态列表
const checkInList = ref([
  {
    id: 1,
    user: {
      name: '张小美',
      avatar: 'https://via.placeholder.com/80x80/4A90E2/FFFFFF?text=张',
      checkInDays: 20
    },
    content:
      '好的界面设计并不给于图片，而是给于对于人的理解。比如人们喜欢什么，为什么人们会试用某种特定的软件，他们可能与之发生交互...',
    images: [
      'https://via.placeholder.com/200x200/FFB6C1/FFFFFF?text=图1',
      'https://via.placeholder.com/200x200/87CEEB/FFFFFF?text=图2',
      'https://via.placeholder.com/200x200/98FB98/FFFFFF?text=图3'
    ],
    createdAt: '2025.04.03',
    likeCount: 0,
    commentCount: 499,
    shareCount: 499,
    isLiked: false,
    canDelete: false
  },
  {
    id: 2,
    user: {
      name: '李小明',
      avatar: 'https://via.placeholder.com/80x80/FF6B6B/FFFFFF?text=李',
      checkInDays: 15
    },
    content:
      '今天完成了阅读任务，感觉收获很大。通过不断的练习，我的理解能力有了明显提升。',
    images: [],
    createdAt: '2025.04.02',
    likeCount: 5,
    commentCount: 12,
    shareCount: 3,
    isLiked: false,
    canDelete: true
  }
])

// 获取页面参数
onMounted(() => {
  // 从路由参数获取任务ID
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const taskId = currentPage.options.id || currentPage.options.taskId

  if (taskId) {
    loadTaskDetail(taskId)
    loadCheckInList(taskId)
  } else {
    // 使用默认数据，不显示错误
    console.log('使用默认任务数据')
  }
})

// 加载任务详情
const loadTaskDetail = async (taskId) => {
  try {
    loading.value = true
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 模拟根据taskId获取不同的任务数据
    console.log('加载任务详情，任务ID:', taskId)

    // 这里可以根据taskId设置不同的任务数据
    if (taskId === '2') {
      taskData.value.title = '英语阅读训练'
      taskData.value.description = '每日英语阅读练习，提升英语水平'
    }
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 加载打卡动态列表
const loadCheckInList = async (taskId, isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
    }

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 300))

    console.log('加载打卡动态，任务ID:', taskId, '是否加载更多:', isLoadMore)

    // 模拟分页数据
    if (isLoadMore) {
      // 模拟没有更多数据
      hasMore.value = false
    }
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 切换描述展开状态
const toggleDescription = () => {
  isDescriptionExpanded.value = !isDescriptionExpanded.value
}

// 处理图片加载错误
const handleImageError = () => {
  console.log('图片加载失败')
}

// 加入/进入打卡
const handleJoinTask = async () => {
  try {
    joinLoading.value = true

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    if (userJoined.value) {
      // 已加入，跳转到打卡页面
      uni.navigateTo({
        url: `/sub-pages/checkin/index?taskId=${taskData.value.id}`
      })
    } else {
      // 未加入，先加入任务
      userJoined.value = true
      taskData.value.userJoined = true
      uni.showToast({
        title: '加入成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  } finally {
    joinLoading.value = false
  }
}

// 预览图片
const previewImage = (images, current) => {
  uni.previewImage({
    urls: images,
    current: current
  })
}

// 切换点赞状态
const toggleLike = async (item) => {
  try {
    // 乐观更新UI
    item.isLiked = !item.isLiked
    item.likeCount += item.isLiked ? 1 : -1

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 200))

    console.log('切换点赞状态:', item.id, item.isLiked)
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// 删除打卡动态
const deleteCheckIn = (item) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条打卡动态吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 模拟API调用延迟
          await new Promise((resolve) => setTimeout(resolve, 500))

          const index = checkInList.value.findIndex((i) => i.id === item.id)
          if (index > -1) {
            checkInList.value.splice(index, 1)
          }

          // 更新总数
          taskData.value.totalCheckIns -= 1

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } catch (error) {
          console.log(error)
          uni.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      }
    }
  })
}

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value && taskData.value.id) {
    loadCheckInList(taskData.value.id, true)
  }
})
</script>

<style lang="scss" scoped>
// 自定义样式
.leading-relaxed {
  line-height: 1.6;
}

.line-clamp-3 {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "任务详情",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>
