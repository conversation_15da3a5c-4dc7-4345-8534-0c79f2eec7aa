<!--
 * @Author: Rain
 * @Date: 2025/07/21 12:18:28
 * @LastEditTime: 2025/07/27 16:05:47
 * @Description: 我的打卡动态
-->
<template>
  <view class="w-full flex flex-col p-30 box-border">
    <view class="w-full flex items-center">
      <image src="" mode="scaleToFill" class="w-112rpx h-112rpx mr-20" />
      <view class="h-112rpx flex justify-around flex-col flex-1">
        <view class="text-#333333 text-28rpx font-400">姓名最长就到这里</view>
        <view
          class="w-full flex items-center text-28rpx text-#666666 justify-between"
        >
          <view class="flex items-center flex-1">
            <text class="mr-20">动态:</text>
            <text></text>
          </view>
          <view class="flex items-center flex-1">
            <text class="mr-20">获赞:</text>
            <text></text>
          </view>
          <view class="flex items-center flex-1">
            <text class="mr-20">点评:</text>
            <text></text>
          </view>
        </view>
      </view>
    </view>
    <view class="w-full flex items-center">
      <wd-search
        v-model="keyWord"
        custom-input-class="h-80rpx!"
        custom-class="w-full"
        placeholder-left
      >
        <template #suffix>
          <wd-button
            type="primary"
            custom-class="min-w-100rpx! ml-[-40rpx]!"
            @click="handleChange"
            >搜索</wd-button
          >
        </template>
      </wd-search>
    </view>
    <view
      class="flex w-full items-center justify-center p-30 box-border b-b-1 b-b-solid b-b-#dedede"
    >
      <view
        class="rounded-50% w-32rpx h-32rpx bg-[#2D8FF0] flex items-center justify-center p-20 box-border"
      >
        <wd-icon name="translate-bold" size="14px" color="#ffffff"></wd-icon>
      </view>
      <text class="text-#999999 text-28rpx ml-10">按日期查询</text>
    </view>
    <view class="w-full flex flex-col">
      <view class="w-full flex flex-col">
        <text class="text-#999999 text-28rpx mb-10">2025年</text>
      </view>

      <!-- 打卡动态列表 -->
      <view
        v-for="(item, index) in dynamicList"
        :key="index"
        class="dynamic-item mb-30"
      >
        <!-- 日期显示 -->
        <view class="date-header flex items-center mb-20">
          <text class="text-36rpx font-bold mr-10">{{ item.day }}</text>
          <text class="text-#999999 text-24rpx">{{ item.month }}</text>
        </view>
        <!-- 用户信息和内容 -->
        <view class="user-content bg-white rounded-20rpx p-20 box-border">
          <!-- 用户信息 -->
          <view class="user-info flex items-center mb-20">
            <image
              :src="item.avatar"
              class="w-80rpx h-80rpx rounded-50%"
              mode="aspectFill"
            />
            <view class="ml-20 flex-1">
              <view class="text-30rpx font-600">{{ item.name }}</view>
              <view class="text-#999999 text-24rpx"
                >已坚持{{ item.days }}天</view
              >
            </view>
            <view
              class="share-btn bg-#f0f9ff text-#2D8FF0 text-26rpx py-10 px-30 rounded-40rpx"
            >
              分享海报
            </view>
          </view>
          <!-- 动态内容 -->
          <view class="content-text mb-20">
            <view v-if="item.isExpanded" class="expanded-content">
              <text class="text-28rpx leading-40rpx">{{ item.content }}</text>
              <text class="text-#2D8FF0 ml-10" @click="toggleExpand(index)"
                >收起</text
              >
            </view>
            <view v-else class="truncated-content-wrapper">
              <text class="text-28rpx leading-40rpx truncated-text">{{
                item.content
              }}</text>
              <view v-if="item.needExpand" class="expand-btn-wrapper">
                <text class="text-ellipsis">...</text>
                <text
                  class="text-#2D8FF0 expand-btn"
                  @click="toggleExpand(index)"
                  >展开</text
                >
              </view>
            </view>
          </view>
          <!-- 图片内容 -->
          <view
            v-if="item.images && item.images.length"
            class="image-container flex flex-wrap"
          >
            <image
              v-for="(img, imgIndex) in item.images"
              :key="imgIndex"
              :src="img"
              class="dynamic-image mr-10 mb-10"
              mode="aspectFill"
              @click="previewImage(item.images, imgIndex)"
            />
          </view>

          <view class="w-full p-20 box-border text-#999999 text-28rpx"
            >2025.04.03</view
          >
          <!-- 任务信息 -->
          <view
            v-if="item.task"
            class="task-info bg-#f8f8f8 p-20 rounded-10rpx flex items-center"
          >
            <image src="/static/task-icon.png" class="w-80rpx h-80rpx mr-10" />
            <view
              v-if="item.task.tag"
              class="flex flex-col flex-1 overflow-hidden"
            >
              <view class="text-28rpx">{{ item.task.title }}</view>
              <view class="tag-item text-24rpx py-5 text-#999999">
                {{ item.task.tag }}
              </view>
            </view>
          </view>

          <!-- 互动区域 -->
          <view
            class="interaction-area flex justify-between pt-20 border-t border-#eeeeee"
          >
            <view class="action-btn flex items-center">
              <wd-icon name="like-o" size="20px" color="#999999"></wd-icon>
              <text class="text-#999999 text-26rpx ml-10">{{
                item.likes || 0
              }}</text>
            </view>
            <view class="action-btn flex items-center">
              <wd-icon name="comment-o" size="20px" color="#999999"></wd-icon>
              <text class="text-#999999 text-26rpx ml-10">{{
                item.comments || 0
              }}</text>
            </view>
            <view class="action-btn flex items-center">
              <wd-icon name="share" size="20px" color="#999999"></wd-icon>
              <text class="text-#999999 text-26rpx ml-10">{{
                item.shares || 0
              }}</text>
            </view>
            <view class="action-btn flex items-center">
              <wd-icon name="delete" size="20px" color="#999999"></wd-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 无数据展示 -->
      <view
        v-if="dynamicList.length === 0"
        class="empty-state flex flex-col items-center justify-center py-100"
      >
        <image src="/src/assets/empty.webp" class="w-200rpx h-200rpx mb-20" />
        <text class="text-#999999 text-28rpx">暂无打卡动态</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const keyWord = ref('')
const dynamicList = ref([
  {
    day: '27',
    month: '6月',
    avatar: '/static/avatar.png',
    name: '张小美',
    days: 20,
    content:
      '好的界面设计并不始于图片，而是始于对于人的理解，比如人们喜欢什么，为什么人们会试用某种特定的软件，他们可能与之怎样交互好的界面设计并不始于图片，而是始于对于人的理解，比如人们喜欢什么，为什么人们会试用某种特定的软件，他们可能与之怎样交互',
    needExpand: true,
    isExpanded: false,
    images: ['/static/sample1.jpg', '/static/sample2.jpg'],
    task: {
      title: '这里是一个任务名称',
      tag: '这里是一个打卡标题'
    },
    date: '2025.04.03',
    likes: 0,
    comments: 499,
    shares: 499
  },
  {
    day: '26',
    month: '6月',
    avatar: '/static/avatar.png',
    name: '李明',
    days: 15,
    content: '今天完成了5公里跑步，感觉状态越来越好了！',
    needExpand: false,
    isExpanded: false,
    images: ['/static/run.jpg'],
    task: {
      title: '每日运动打卡',
      tag: '跑步'
    },
    date: '2025.04.02',
    likes: 12,
    comments: 3,
    shares: 1
  }
])

// 展开/收起文本
const toggleExpand = (index) => {
  dynamicList.value[index].isExpanded = !dynamicList.value[index].isExpanded
}

// 检查内容是否需要展开按钮
const checkContentLength = (content) => {
  // 假设每行平均30个字符，3行约90个字符
  return content.length > 90
}

// 预览图片
const previewImage = (images, current) => {
  uni.previewImage({
    urls: images,
    current: images[current]
  })
}

// 初始化
onMounted(() => {
  console.log('mounted')
  // 检查每个动态内容是否需要展开按钮
  dynamicList.value.forEach((item) => {
    item.needExpand = checkContentLength(item.content)
  })
  // 这里可以添加获取动态列表的API调用
})

// 搜索处理
const handleChange = (value) => {
  console.log('搜索关键词:', value)
  // 实现搜索逻辑
  if (value) {
    // 过滤动态列表
    const filteredList = dynamicList.value.filter(
      (item) =>
        item.content.includes(value) ||
        item.name.includes(value) ||
        (item.task && item.task.title.includes(value))
    )
    // 这里可以更新显示的列表或者调用API进行搜索
    console.log('过滤结果:', filteredList)
  }
}
</script>
<style scoped lang="scss">
.dynamic-item {
  width: 100%;
}

.dynamic-image {
  width: 220rpx;
  height: 220rpx;
  border-radius: 10rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
}

.content-text {
  text-align: justify;
  word-break: break-all;
}

.content-text {
  text-align: justify;
  word-break: break-all;
}

.truncated-content-wrapper {
  position: relative;
  max-height: 120rpx; /* 约3行文字高度 */
  overflow: hidden;
}

.truncated-text {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.expand-btn-wrapper {
  position: absolute;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding-left: 5rpx;
  background: #ffffff;
}

.text-ellipsis {
  margin-right: 5rpx;
  color: #333333;
}

.expand-btn {
  color: #2d8ff0;
}
::v-deep(.wd-search) {
  .wd-search__cover {
    height: 40px !important;
  }
}
</style>
<route lang="json">
{
  "style": { "navigationBarTitleText": "我的打卡动态" }
}
</route>
