<!--
 * @Author: Rain
 * @Date: 2025/07/21 11:42:05
 * @LastEditTime: 2025/07/26 15:27:14
 * @Description: Do Something
-->
<template>
  <view class="w-full flex flex-col items-center bg-white">
    <view
      class="w-full flex items-center justify-between px-30 py-30 box-border b-b-solid b-b-[2rpx] b-#eeeeee"
    >
      <text class="#333333 text-28rpx font-400 whitespace-nowrap"
        >修改头像</text
      >
      <view class="flex items-center" @tap="chooseImage">
        <image
          src=""
          class="w-88rpx h-88rpx rounded-[50%] mr-30"
          mode="scaleToFill"
        />
        <wd-icon name="arrow-right" size="18px" color="#999999"></wd-icon>
      </view>
    </view>
    <view
      class="w-full flex items-center justify-between py-30 px-30 box-border"
    >
      <text class="#333333 text-28rpx font-400 whitespace-nowrap"
        >修改昵称</text
      >
      <view class="flex items-center" @tap="show = true">
        <text class="text-#666666 text-28rpx">姓名最长就到这里</text>
        <wd-icon name="arrow-right" size="18px" color="#999999"></wd-icon>
      </view>
    </view>
    <wd-popup
      v-model="show"
      custom-style="border-radius:32rpx;"
      @close="handleClose"
    >
      <view
        class="flex w-600rpx flex flex-col items-center bg-white rounded-[30rpx] overflow-hidden"
      >
        <text class="text-#333333 text-32rpx font-600 p-30 box-border"
          >修改昵称</text
        >
        <view class="w-full p-30 box-border pt-10">
          <wd-input
            v-model="nickName"
            placeholder="请输入昵称"
            clearable
            no-border
            class="w-full h-88rpx rounded-[40rpx] bg-[#F7F8FA] text-#666666 text-28rpx"
          ></wd-input>
        </view>
        <view class="w-full flex items-center mt-30">
          <wd-button
            :round="false"
            type="info"
            custom-class="flex-1 h-88rpx! rounded-none! bg-[#f1f1f1]!"
          >
            取消
          </wd-button>
          <wd-button
            :round="false"
            custom-class="flex-1 h-88rpx! rounded-none!"
          >
            确定
          </wd-button>
        </view>
      </view>
    </wd-popup>
    <wd-img-cropper
      v-model="showCropper"
      :img-src="avatar"
      @confirm="handleConfirmUpload"
      @cancel="handleCancel"
    >
    </wd-img-cropper>
  </view>
</template>

<script setup>
const show = ref(false)
const showCropper = ref(false)
const nickName = ref('')
const avatar = ref('')
const handleClose = () => {
  show.value = false
}

const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const { tempFilePaths } = res
      avatar.value = tempFilePaths.at(0)
      showCropper.value = true
    }
  })
}
const handleCancel = () => {
  showCropper.value = false
}
const handleConfirmUpload = () => {
  showCropper.value = false
}
</script>
<style scoped lang="scss"></style>
<route lang="json">
{
  "style": { "navigationBarTitleText": "修改资料" }
}
</route>
