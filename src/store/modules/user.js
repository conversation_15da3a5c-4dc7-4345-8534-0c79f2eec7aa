/**
 * @Author: Rain
 * @Date: 2025/07/17 20:54:50
 * @LastEditTime: 2025/08/11 14:42:12
 * @Description: Do Something
 */
import { defineStore } from 'pinia'
import { wxLogin, phoneLogin } from '@/apis/login'

export const useUserStore = defineStore('user', {
  state: () => ({
    memberInfo: {},
    token: ''
  }),
  getters: {
    userInfo: (state) => state.memberInfo
  },
  actions: {
    /** 设置用户信息 */
    setUserInfo(data) {
      this.memberInfo = data
    },
    /** 设置请求token */
    setToken(payload) {
      this.token = payload
    },
    async wxLoginAction({ encryptedData, iv }) {
      if (!iv || !encryptedData) return
      return new Promise((resolve, reject) => {
        uni.login({
          success: (res) => {
            const { code } = res
            wxLogin({
              code,
              encryptedData,
              iv
            })
              .then((res) => {
                const { memberInfo } = res
                const { randomIdMini } = memberInfo
                this.setToken(randomIdMini)
                this.setUserInfo(memberInfo)
                uni.setStorageSync('token', randomIdMini)
                resolve(res)
              })
              .catch((err) => {
                console.log(err)
                reject(err)
              })
          }
        })
      })
    },
    //手机号和验证码登录
    async phoneLoginAction(params) {
      if (!params.phoneNum || !params.verifyCode) return
      await phoneLogin(params)
        .then((res) => {
          const { memberInfo } = res
          const { randomIdMini } = memberInfo
          this.setToken(randomIdMini)
          this.setUserInfo(memberInfo)
          uni.setStorageSync('token', randomIdMini)
        })
        .catch((err) => {
          console.log(err)
        })
    }
  },
  // unistorage: true
  // 缓存指定参数
  unistorage: {
    key: 'user', // 缓存key, 默认当前模块
    paths: ['token', 'memberInfo']
  }
})
