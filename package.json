{"name": "signApp", "version": "1.0.0", "description": "打卡小程序", "main": "index.js", "scripts": {"dev": "uni -p mp-weixin", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:app-harmony": "uni -p app-harmony", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build": "uni build -p mp-weixin", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:app-harmony": "uni build -p app-harmony", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "postinstall": "simple-git-hooks", "lint": "npx oxlint && eslint --fix."}, "keywords": [], "author": "", "license": "MIT", "nano-staged": {"*.md": "prettier --write", "*.{ts,tsx,js,vue,scss}": "prettier --write", "*.{ts,tsx,js,vue}": "pnpm lint", "*.{css,scss,vue,html}": "stylelint --fix"}, "simple-git-hooks": {"pre-commit": "npx nano-staged", "commit-msg": "npx node ./verify-commit.mjs"}, "engines": {"node": ">= 18.0.0"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-**********711001", "@dcloudio/uni-app-harmony": "3.0.0-**********711001", "@dcloudio/uni-app-plus": "3.0.0-**********711001", "@dcloudio/uni-components": "3.0.0-**********711001", "@dcloudio/uni-h5": "3.0.0-**********711001", "@dcloudio/uni-mp-alipay": "3.0.0-**********711001", "@dcloudio/uni-mp-baidu": "3.0.0-**********711001", "@dcloudio/uni-mp-harmony": "3.0.0-**********711001", "@dcloudio/uni-mp-jd": "3.0.0-**********711001", "@dcloudio/uni-mp-kuaishou": "3.0.0-**********711001", "@dcloudio/uni-mp-lark": "3.0.0-**********711001", "@dcloudio/uni-mp-qq": "3.0.0-**********711001", "@dcloudio/uni-mp-toutiao": "3.0.0-**********711001", "@dcloudio/uni-mp-weixin": "3.0.0-**********711001", "@dcloudio/uni-mp-xhs": "3.0.0-**********711001", "@dcloudio/uni-quickapp-webview": "3.0.0-**********711001", "@uni-helper/axios-adapter": "^1.5.2", "axios": "^1.7.7", "mescroll-uni-latest": "^1.3.8", "pinia": "^3.0.3", "pinia-plugin-unistorage": "^0.1.2", "vue": "^3.4.21", "vue-i18n": "^9.14.5", "wot-design-uni": "^1.10.0"}, "devDependencies": {"@dcloudio/types": "^3.4.16", "@dcloudio/uni-automator": "3.0.0-**********711001", "@dcloudio/uni-cli-shared": "3.0.0-**********711001", "@dcloudio/uni-stacktracey": "3.0.0-**********711001", "@dcloudio/vite-plugin-uni": "3.0.0-**********711001", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@iconify/json": "^2.2.360", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@unocss/preset-legacy-compat": "^0.63.6", "@vitejs/plugin-legacy": "^5.4.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/runtime-core": "^3.4.21", "eslint": "^9.9.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-oxlint": "^1.7.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "nano-staged": "^0.8.0", "oxlint": "^1.7.0", "picocolors": "^1.1.1", "postcss": "^8.4.40", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "sass": "1.78.0", "simple-git-hooks": "^2.11.1", "stylelint": "^16.8.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-prettier": "^5.0.2", "typescript-eslint": "^8.37.0", "uni-mini-router": "^0.1.6", "uni-parse-pages": "^0.0.1", "unocss": "^0.63.4", "unocss-applet": "^0.8.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^5.2.8", "vite-plugin-restart": "^0.4.1", "vite-plugin-vue-setup-extend": "^0.4.0"}, "prettier": {"singleQuote": true, "trailingComma": "none", "semi": false}}