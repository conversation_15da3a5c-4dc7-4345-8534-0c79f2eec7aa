<!--
 * @Author: Rain
 * @Date: 2025/07/17 13:59:11
 * @LastEditTime: 2025/07/19 00:18:54
 * @Description: 产品开发指导文档
-->
---
inclusion: always
---

# 产品开发指导

**signApp** 是一个面向微信等小程序平台的打卡小程序应用。

## 架构模式
- 遵循 uni-app 跨平台兼容性约定
- 所有新组件使用 Vue 3 Composition API
- 使用 rpx 单位实现小程序兼容的响应式设计
- 使用 uni-app 页面生命周期方法构建页面结构

## 代码风格与约定
- 业务逻辑和面向用户的功能使用中文注释
- JavaScript/Vue 变量和函数使用驼峰命名法
- 组件文件名和 CSS 类名使用短横线命名法
- 自定义组件使用项目特定的命名空间前缀
- 组件属性和事件在中英文语境下保持描述性

## 平台特定考虑
- 主要目标：微信小程序 (mp-weixin) - 优先针对此平台优化
- 在 H5 和原生应用上测试跨平台兼容性
- 需要平台特定代码时使用条件编译 (#ifdef MP-WEIXIN)
- 确保 UI 组件在所有目标平台上正常工作

## 用户体验指导
- 针对中国用户和文化偏好进行设计
- 实现直观的打卡流程，提供清晰的视觉反馈
- 使用适当的中文排版和间距
- 考虑微信设计语言和用户期望
- 优化移动端优先的体验

## 开发实践
- 使用 Pinia 进行状态管理，通过 uni-storage 实现持久化
- 为网络请求和用户操作实现适当的错误处理
- 遵循 uni-app 页面路由约定
- 使用 wot-design-uni 组件保持 UI 一致性
- 确保所有交互都有适当的加载状态和用户反馈
- 优先使用unocss
- 页面不需要手动导入(import) vue 相关的 API 由unplugin-auto-import自动导入
